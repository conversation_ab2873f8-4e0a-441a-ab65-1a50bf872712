#!/bin/bash

# 应用数据库结构更改脚本
# 用途: 为ranking_details表添加team_name字段

echo "=== 燕友圈榜单系统 - 数据库结构更新 ==="
echo "任务: 为ranking_details表添加team_name字段"
echo ""

# 检查是否在正确的目录
if [ ! -f "alembic.ini" ]; then
    echo "❌ 错误: 请在项目根目录下运行此脚本"
    exit 1
fi

# 方法1: 使用Alembic迁移（推荐）
echo "方法1: 使用Alembic迁移"
echo "执行命令: alembic upgrade head"
echo ""

read -p "是否使用Alembic执行迁移? (y/n): " use_alembic

if [ "$use_alembic" = "y" ] || [ "$use_alembic" = "Y" ]; then
    echo "正在执行Alembic迁移..."
    alembic upgrade head
    
    if [ $? -eq 0 ]; then
        echo "✅ Alembic迁移执行成功!"
    else
        echo "❌ Alembic迁移执行失败"
        exit 1
    fi
else
    echo "跳过Alembic迁移"
fi

echo ""
echo "方法2: 直接执行SQL脚本"
echo "可用的SQL脚本:"
echo "  - sql/add_team_name_field.sql (仅添加team_name字段)"
echo "  - sql/fix_ranking_details_structure.sql (完整结构修正)"
echo ""

read -p "是否要查看SQL脚本内容? (y/n): " show_sql

if [ "$show_sql" = "y" ] || [ "$show_sql" = "Y" ]; then
    echo ""
    echo "=== add_team_name_field.sql 内容 ==="
    cat sql/add_team_name_field.sql
    echo ""
    echo "=== 脚本内容结束 ==="
fi

echo ""
echo "验证数据库结构:"
echo "执行命令: python scripts/verify_database_structure.py"

read -p "是否现在验证数据库结构? (y/n): " verify_db

if [ "$verify_db" = "y" ] || [ "$verify_db" = "Y" ]; then
    echo "正在验证数据库结构..."
    python scripts/verify_database_structure.py
fi

echo ""
echo "=== 数据库更新完成 ==="
echo "如果使用手动SQL执行，请运行:"
echo "  mysql -u [username] -p [database] < sql/add_team_name_field.sql"
echo ""
echo "或者连接到MySQL后执行:"
echo "  ALTER TABLE ranking_details ADD COLUMN team_name VARCHAR(100) NULL COMMENT '队伍名称';"
