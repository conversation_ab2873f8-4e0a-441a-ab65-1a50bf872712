"""Add team_name field and update ranking_details structure

Revision ID: 003_add_team_name_and_update_ranking_details
Revises: 002_add_user_profile_fields
Create Date: 2024-12-19 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '003_add_team_name_and_update_ranking_details'
down_revision = '002_add_user_profile_fields'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库：更新ranking_details表结构，添加team_name字段"""
    
    # 添加新字段
    op.add_column('ranking_details', sa.Column('participant_name', sa.String(length=100), nullable=False, comment='参与者姓名'))
    op.add_column('ranking_details', sa.Column('team_name', sa.String(length=100), nullable=True, comment='队伍名称'))
    op.add_column('ranking_details', sa.Column('rank_range', sa.String(length=20), nullable=True, comment='排名范围'))
    op.add_column('ranking_details', sa.Column('notes', sa.Text(), nullable=True, comment='备注'))
    
    # 修改现有字段
    op.alter_column('ranking_details', 'completion_seconds',
                   existing_type=sa.Integer(),
                   nullable=True,
                   comment='完成时间（秒）')
    
    # 删除不再需要的字段
    op.drop_column('ranking_details', 'rank_start')
    op.drop_column('ranking_details', 'rank_end')
    op.drop_column('ranking_details', 'completion_time')
    op.drop_column('ranking_details', 'participant_count')
    op.drop_column('ranking_details', 'team_info')


def downgrade() -> None:
    """降级数据库：恢复原有的ranking_details表结构"""
    
    # 恢复原有字段
    op.add_column('ranking_details', sa.Column('rank_start', sa.Integer(), nullable=False, comment='排名开始'))
    op.add_column('ranking_details', sa.Column('rank_end', sa.Integer(), nullable=False, comment='排名结束'))
    op.add_column('ranking_details', sa.Column('completion_time', sa.Time(), nullable=False, comment='完成时间(分秒)'))
    op.add_column('ranking_details', sa.Column('participant_count', sa.Integer(), nullable=False, comment='当前时间区间参与人数'))
    op.add_column('ranking_details', sa.Column('team_info', sa.Text(), nullable=True, comment='队伍信息(JSON格式)'))
    
    # 恢复completion_seconds字段的原有属性
    op.alter_column('ranking_details', 'completion_seconds',
                   existing_type=sa.Integer(),
                   nullable=False,
                   comment='完成时间(总秒数)')
    
    # 删除新添加的字段
    op.drop_column('ranking_details', 'notes')
    op.drop_column('ranking_details', 'rank_range')
    op.drop_column('ranking_details', 'team_name')
    op.drop_column('ranking_details', 'participant_name')
