#!/usr/bin/env python3
"""
测试数据库会话管理修复和Swagger文档显示
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试关键模块导入"""
    print("🔍 测试模块导入...")

    try:
        # 测试API端点导入
        from app.api.v1.endpoints import system_config, content
        print("✅ 系统配置和内容管理API端点导入成功")

        # 测试schemas导入
        from app.schemas import system_config as sc_schemas, content as content_schemas
        print("✅ Schemas导入成功")

        # 测试服务导入
        from app.services import system_config_service, content_service
        print("✅ 服务层导入成功")

        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_app_creation():
    """测试应用创建"""
    print("\n🚀 测试应用创建...")

    try:
        from app.main import app
        print("✅ 应用导入成功")

        # 获取OpenAPI schema
        openapi_schema = app.openapi()
        print("✅ OpenAPI schema生成成功")

        return openapi_schema
    except Exception as e:
        print(f"❌ 应用创建失败: {e}")
        return None
